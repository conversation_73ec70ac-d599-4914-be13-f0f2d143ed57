<?php

use App\Http\Controllers\Api\BlockController;
use App\Http\Controllers\Api\MachineController;
use App\Http\Controllers\Api\StaffAuthController;
use App\Http\Controllers\Api\TonPaymentController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\VipPlanController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// =============================================================================
// PUBLIC ROUTES - No authentication required
// =============================================================================

Route::prefix('public')->group(function () {
    // API Health Check
    Route::get('/health', function () {
        return response()->json([
            'status' => 'ok',
            'timestamp' => now(),
            'service' => 'backend-api'
        ]);
    });

    // Public endpoints
    Route::get('/info', function () {
        return response()->json([
            'app' => config('app.name'),
            'version' => '1.0.0',
            'endpoints' => [
                'public' => '/api/public/*',
                'auth' => '/api/auth/* (user authentication)',
                'user' => '/api/user/* (requires user token)',
                'admin' => '/api/admin/* (requires admin credentials)',
                'staff' => '/api/staff/* (requires staff token)'
            ]
        ]);
    });
});

// =============================================================================
// AUTHENTICATION ROUTES - Token-based authentication
// =============================================================================

Route::prefix('auth')->group(function () {
    // User authentication (no password required)

});

// =============================================================================
// USER ROUTES - User management (legacy support)
// =============================================================================

// =============================================================================
// USER ROUTES - Protected user endpoints
// =============================================================================

Route::prefix('user')->middleware('user.auth')->group(function () {

});

// =============================================================================
// ADMIN ROUTES - Admin authentication required
// =============================================================================

Route::prefix('admin')->middleware('admin.auth')->group(function () {
    Route::post('/login', [StaffAuthController::class, 'login']);

    // Protected staff routes (authentication required)
    Route::middleware('auth:staff')->group(function () {
        Route::post('/logout', [StaffAuthController::class, 'logout']);
        Route::get('/me', [StaffAuthController::class, 'me']);
    });
});
