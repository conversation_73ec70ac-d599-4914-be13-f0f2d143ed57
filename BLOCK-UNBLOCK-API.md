# Block/Unblock User API Documentation

## Overview
The Block/Unblock API system allows staff members to block and unblock users, preventing blocked users from accessing other features. The system includes middleware to automatically check if users are blocked.

## Authentication
All block/unblock operations require **Staff Authentication** using Laravel Sanctum tokens.

## Endpoints

### 1. Block User
- **URL**: `/api/admin/users/block`
- **Method**: `POST`
- **Authentication**: Required (Staff token)

#### Request Payload
```json
{
    "user_id": 123,
    "reason": "Violation of terms of service"
}
```

#### Validation Rules
| Field | Rules |
|-------|--------|
| user_id | required, integer, exists:users,id |
| reason | required, string, max:500 |

#### Success Response (201 Created)
```json
{
    "success": true,
    "message": "User blocked successfully",
    "data": {
        "block_id": 1,
        "user": {
            "id": 123,
            "name": "<PERSON>",
            "tele_id": "123456789"
        },
        "reason": "Violation of terms of service",
        "blocked_at": "2025-06-10T18:00:00.000000Z",
        "blocked_by": {
            "staff_id": 1,
            "staff_name": "admin"
        }
    }
}
```

#### Error Response - Already Blocked (409 Conflict)
```json
{
    "success": false,
    "message": "User is already blocked",
    "data": {
        "user_id": 123,
        "current_reason": "Previous violation",
        "blocked_at": "2025-06-09T15:30:00.000000Z",
        "blocked_by_staff_id": 1
    }
}
```

### 2. Unblock User
- **URL**: `/api/admin/users/unblock`
- **Method**: `POST`
- **Authentication**: Required (Staff token)

#### Request Payload
```json
{
    "user_id": 123
}
```

#### Validation Rules
| Field | Rules |
|-------|--------|
| user_id | required, integer, exists:users,id |

#### Success Response (200 OK)
```json
{
    "success": true,
    "message": "User unblocked successfully",
    "data": {
        "user": {
            "id": 123,
            "name": "John Doe",
            "tele_id": "123456789"
        },
        "unblocked_at": "2025-06-10T18:05:00.000000Z",
        "unblocked_by": {
            "staff_id": 1,
            "staff_name": "admin"
        },
        "previous_block": {
            "block_id": 1,
            "original_reason": "Violation of terms of service",
            "blocked_at": "2025-06-10T18:00:00.000000Z",
            "blocked_by_staff_id": 1
        }
    }
}
```

#### Error Response - User Not Blocked (404 Not Found)
```json
{
    "success": false,
    "message": "User is not blocked",
    "data": {
        "user_id": 123
    }
}
```

### 3. Get Blocked Users List
- **URL**: `/api/admin/users/blocked`
- **Method**: `GET`
- **Authentication**: Required (Staff token)

#### Success Response (200 OK)
```json
{
    "success": true,
    "message": "Blocked users retrieved successfully",
    "data": {
        "blocked_users": [
            {
                "id": 1,
                "user_id": 123,
                "staff_id": 1,
                "reason": "Spam behavior",
                "created_at": "2025-06-10T18:00:00.000000Z",
                "updated_at": "2025-06-10T18:00:00.000000Z",
                "user": {
                    "id": 123,
                    "name": "John Doe",
                    "tele_id": "123456789"
                },
                "staff": {
                    "id": 1,
                    "username": "admin"
                }
            }
        ],
        "pagination": {
            "current_page": 1,
            "total_pages": 1,
            "total_items": 1,
            "per_page": 20
        }
    }
}
```

## Blocked User Middleware

### Purpose
The `CheckUserBlocked` middleware automatically prevents blocked users from accessing protected features.

### Applied To
- Protected user endpoints that require authentication
- Can be applied to any authenticated user-facing endpoints
- **Note**: `/api/users/sync` is a public endpoint and does not use this middleware

### Blocked User Response (403 Forbidden)
When a blocked user tries to access a protected endpoint:
```json
{
    "success": false,
    "message": "Account blocked",
    "reason": "Violation of terms of service",
    "blocked_at": "2025-06-10T18:00:00.000000Z",
    "error_code": "USER_BLOCKED"
}
```

### How It Works
1. Extracts user ID from request (supports multiple sources):
   - `user_id` parameter
   - Route parameter `user_id`
   - `telegram_id` parameter (looks up user by tele_id)
2. Checks if user exists in blocked table
3. Returns blocked response if user is blocked
4. Continues to next middleware if user is not blocked

## Authentication Errors

### No Token (401 Unauthorized)
```json
{
    "success": false,
    "message": "Authorization token required"
}
```

### Invalid Token (401 Unauthorized)
```json
{
    "success": false,
    "message": "Invalid or expired admin token"
}
```

### Non-Staff User (403 Forbidden)
```json
{
    "success": false,
    "message": "Admin access required"
}
```

## Example Usage

### Block a User
```bash
curl -X POST "http://your-domain.com/api/admin/users/block" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_STAFF_TOKEN" \
     -d '{
         "user_id": 123,
         "reason": "Spam behavior detected"
     }'
```

### Unblock a User
```bash
curl -X POST "http://your-domain.com/api/admin/users/unblock" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_STAFF_TOKEN" \
     -d '{
         "user_id": 123
     }'
```

### Get Blocked Users
```bash
curl -X GET "http://your-domain.com/api/admin/users/blocked" \
     -H "Authorization: Bearer YOUR_STAFF_TOKEN"
```

### JavaScript Example
```javascript
// Block user
const blockUser = async (userId, reason, staffToken) => {
    const response = await fetch('/api/admin/users/block', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${staffToken}`
        },
        body: JSON.stringify({
            user_id: userId,
            reason: reason
        })
    });

    return await response.json();
};

// Unblock user
const unblockUser = async (userId, staffToken) => {
    const response = await fetch('/api/admin/users/unblock', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${staffToken}`
        },
        body: JSON.stringify({
            user_id: userId
        })
    });

    return await response.json();
};
```

## Database Schema

### Blocked Table
- `id` - Primary key
- `user_id` - Foreign key to users table
- `staff_id` - Foreign key to staff table (who blocked the user)
- `reason` - Text reason for blocking (max 500 characters)
- `created_at` - When user was blocked
- `updated_at` - Last update timestamp

## Security Features

1. **Staff-Only Access**: Only authenticated staff can block/unblock users
2. **Token Validation**: All requests require valid Sanctum tokens
3. **Audit Trail**: Records which staff member blocked/unblocked users
4. **Automatic Prevention**: Middleware automatically blocks access for blocked users
5. **Reason Tracking**: All blocks include a reason for accountability

## Notes

- Blocking a user immediately prevents them from using protected features
- Unblocking a user immediately restores their access
- Block records are deleted when users are unblocked (not soft deleted)
- The system supports pagination for the blocked users list (20 per page)
- All timestamps are in UTC format
