# Factory Improvements

## Overview
Refactored test files to use Laravel Factories instead of manual model creation for cleaner, more maintainable tests.

## Created Factories

### 1. UserFactory
```php
// Before (manual creation)
User::create([
    'tele_id' => '123456789',
    'name' => 'Test User',
    'country' => 'US',
    'language_code' => 'en',
    'last_active' => now(),
    'balance_ton' => 0,
    'balance_token' => 0,
]);

// After (using factory)
User::factory()->create([
    'tele_id' => '123456789',
    'name' => 'Test User',
]);
```

**Features:**
- Generates realistic fake data (country codes, language codes, names)
- Automatically sets default values for all required fields
- Cleaner test setup with less code duplication

### 2. StaffFactory
```php
// Before (manual creation)
Staff::create([
    'username' => 'teststaff',
    'password' => bcrypt('password'),
]);

// After (using factory)
Staff::factory()->withUsername('teststaff')->create();
```

**Features:**
- `withUsername()` method for setting specific usernames
- Auto-generates unique usernames and hashed passwords
- Consistent password hashing

### 3. BlockedFactory
```php
// Before (manual creation)
Blocked::create([
    'user_id' => $user->id,
    'staff_id' => $staff->id,
    'reason' => 'Spam behavior'
]);

// After (using factory)
Blocked::factory()
    ->forUserAndStaff($user, $staff)
    ->withReason('Spam behavior')
    ->create();
```

**Features:**
- `forUserAndStaff()` method for setting specific relationships
- `withReason()` method for custom block reasons
- Auto-generates realistic reasons when not specified
- Creates related User and Staff models automatically

## Benefits

### 1. Cleaner Code
- Reduced boilerplate in test files
- Less repetitive model creation code
- More readable test setup

### 2. Consistency
- All models use same creation pattern
- Consistent default values across tests
- Standardized fake data generation

### 3. Maintainability
- Factory changes automatically update all tests
- Easy to add new model fields
- Centralized model creation logic

### 4. Flexibility
- Custom methods for common scenarios
- Override specific fields when needed
- Chain multiple state modifications

## Usage Examples

### Basic Usage
```php
// Create with defaults
$user = User::factory()->create();
$staff = Staff::factory()->create();
$blocked = Blocked::factory()->create();
```

### With Overrides
```php
// Override specific fields
$user = User::factory()->create([
    'name' => 'Specific Name',
    'country' => 'US'
]);
```

### Using Custom Methods
```php
// Staff with specific username
$staff = Staff::factory()->withUsername('admin')->create();

// Blocked relationship with custom reason
$blocked = Blocked::factory()
    ->forUserAndStaff($user, $staff)
    ->withReason('Custom reason')
    ->create();
```

### Multiple Models
```php
// Create multiple instances
$users = User::factory()->count(5)->create();
$staff = Staff::factory()->count(3)->create();
```

## Test Files Updated

### BlockUnblockTest.php
- **Before**: 15+ lines for creating User, Staff, and Blocked models
- **After**: 2-3 lines using factories
- **Improvement**: 80% reduction in setup code

### UserSyncTest.php
- **Before**: Manual User creation with all fields specified
- **After**: Factory with only necessary field overrides
- **Improvement**: Cleaner, more focused test data

### StaffAuthTest.php
- **Before**: Manual Staff creation with password hashing
- **After**: Factory with automatic password handling
- **Improvement**: More reliable password generation

## Migration Benefits

1. **Immediate**: Tests become cleaner and easier to read
2. **Short-term**: Easier to maintain and modify test data
3. **Long-term**: Scalable pattern for future test development

## Best Practices

1. **Use factories by default** for all model creation in tests
2. **Override only necessary fields** - let factories handle defaults
3. **Create custom factory methods** for common patterns
4. **Keep factory definitions simple** - complex logic belongs in tests
5. **Use descriptive method names** like `withUsername()`, `forUserAndStaff()`

## Before vs After Comparison

### Test Setup Complexity
- **Before**: ~20 lines to create test models with all relationships
- **After**: ~5 lines using factory chains and custom methods
- **Improvement**: 75% reduction in setup code

### Maintainability
- **Before**: Changes to model structure require updating multiple test files
- **After**: Changes only need updates in factory definitions
- **Improvement**: Centralized maintenance

### Readability
- **Before**: Tests cluttered with model creation boilerplate
- **After**: Tests focus on business logic and assertions
- **Improvement**: Better separation of concerns
